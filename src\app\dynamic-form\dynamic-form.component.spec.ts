import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicFormComponent } from './dynamic-form.component';

describe('DynamicFormComponent', () => {
  let component: DynamicFormComponent;
  let fixture: ComponentFixture<DynamicFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DynamicFormComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DynamicFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // NEW: Grid positioning system tests
  describe('Grid Positioning System', () => {
    it('should detect grid positioning when fields have row and column', () => {
      const fieldsWithPositioning = [
        { fieldName: 'field1', row: 1, column: 1 },
        { fieldName: 'field2', row: 1, column: 2 }
      ];

      const result = component['hasGridPositioning'](fieldsWithPositioning);
      expect(result).toBe(true);
    });

    it('should not detect grid positioning when fields lack positioning data', () => {
      const fieldsWithoutPositioning = [
        { fieldName: 'field1', type: 'string' },
        { fieldName: 'field2', type: 'string' }
      ];

      const result = component['hasGridPositioning'](fieldsWithoutPositioning);
      expect(result).toBe(false);
    });

    it('should calculate correct grid position', () => {
      const field = { fieldName: 'test', row: 2, column: 3, rowSize: 2, colSize: 1 };

      const result = component['calculateFieldPosition'](field);

      expect(result.gridRow).toBe(2);
      expect(result.gridColumn).toBe(3);
      expect(result.gridRowEnd).toBe(4);
      expect(result.gridColumnEnd).toBe(4);
      expect(result.cssGridRow).toBe('2 / 4');
      expect(result.cssGridColumn).toBe('3 / 4');
    });

    it('should calculate max rows correctly', () => {
      const fields = [
        { row: 1, rowSize: 1 },
        { row: 2, rowSize: 3 },
        { row: 4, rowSize: 1 }
      ];

      const result = component['calculateMaxRows'](fields);
      expect(result).toBe(5); // row 2 + rowSize 3 - 1 = 4, but row 4 exists so max is 5
    });

    it('should detect mobile view correctly', () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });

      component['detectMobileView']();
      expect(component.isMobileView).toBe(true);

      // Test desktop width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });

      component['detectMobileView']();
      expect(component.isMobileView).toBe(false);
    });

    it('should set layout system correctly for grid positioning', () => {
      const fieldsWithPositioning = [
        { fieldName: 'field1', row: 1, column: 1 }
      ];

      component['detectLayoutSystem'](fieldsWithPositioning, 2);

      expect(component.useGridPositioning).toBe(true);
      expect(component.useSingleColumn).toBe(false);
      expect(component.columnCount).toBe(2);
    });

    it('should set layout system correctly for single column fallback', () => {
      const fieldsWithoutPositioning = [
        { fieldName: 'field1', type: 'string' }
      ];

      component['detectLayoutSystem'](fieldsWithoutPositioning, 3);

      expect(component.useGridPositioning).toBe(false);
      expect(component.useSingleColumn).toBe(true);
      expect(component.columnCount).toBe(1);
      expect(component.singleColumnFields).toEqual(fieldsWithoutPositioning);
    });
  });

  // Test backward compatibility
  describe('Backward Compatibility', () => {
    it('should preserve existing properties', () => {
      expect(component.columns).toBeDefined();
      expect(component.fields).toBeDefined();
      expect(component.columnCount).toBeDefined();
    });

    it('should have existing methods available', () => {
      expect(typeof component['distributeFieldsRoundRobin']).toBe('function');
      expect(typeof component['orderFieldsBasedOnFormDefinition']).toBe('function');
    });
  });
});
